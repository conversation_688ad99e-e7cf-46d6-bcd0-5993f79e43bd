<?php
/**
 * 支付结果异步通知接收文件
 * 接收易支付平台的支付成功通知，验证签名后更新订单状态
 */

// 记录日志函数
function writeNotifyLog($message, $level = 'INFO') {
    $log_dir = __DIR__ . '/log';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0777, true);
    }
    $log_file = $log_dir . '/notify_' . date('Y-m-d') . '.log';
    $log_time = date('Y-m-d H:i:s');
    $log_content = "[$log_time] [$level] $message\n";
    file_put_contents($log_file, $log_content, FILE_APPEND);
}

// 记录收到的通知
$raw_input = file_get_contents('php://input');
$get_params = $_GET;
$post_params = $_POST;

writeNotifyLog("收到支付通知");
writeNotifyLog("GET参数: " . json_encode($get_params, JSON_UNESCAPED_UNICODE));
writeNotifyLog("POST参数: " . json_encode($post_params, JSON_UNESCAPED_UNICODE));
writeNotifyLog("原始输入: " . $raw_input);

// 获取通知参数（优先使用GET参数，如果没有则使用POST参数）
$params = !empty($get_params) ? $get_params : $post_params;

// 必需的参数
$required_params = ['pid', 'trade_no', 'out_trade_no', 'type', 'name', 'money', 'trade_status', 'sign', 'sign_type'];

// 验证必需参数
foreach ($required_params as $param) {
    if (!isset($params[$param]) || $params[$param] === '') {
        writeNotifyLog("缺少必需参数: $param", 'ERROR');
        echo 'fail';
        exit;
    }
}

// 提取参数
$pid = $params['pid'];
$trade_no = $params['trade_no'];
$out_trade_no = $params['out_trade_no']; // 商户订单号
$type = $params['type'];
$name = $params['name'];
$money = $params['money'];
$trade_status = $params['trade_status'];
$param = $params['param'] ?? '';
$sign = $params['sign'];
$sign_type = $params['sign_type'];

writeNotifyLog("解析参数 - 订单号: $out_trade_no, 支付状态: $trade_status, 金额: $money");

// 验证商户ID
$expected_pid = "2222"; // 与create_order.php中的配置保持一致
if ($pid !== $expected_pid) {
    writeNotifyLog("商户ID验证失败: 期望 $expected_pid, 实际 $pid", 'ERROR');
    echo 'fail';
    exit;
}

// 验证签名
$key = "4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA"; // 与create_order.php中的配置保持一致

// 1. 筛选参数（排除空值和以sign开头的参数）
$para_filter = [];
foreach ($params as $k => $v) {
    if ($v != "" && strpos($k, "sign") !== 0) {
        $para_filter[$k] = $v;
    }
}

// 2. 排序（按ASCII码递增排序）
ksort($para_filter);

// 3. 拼接参数（格式：key=value&key=value）
$prestr_parts = [];
foreach ($para_filter as $k => $v) {
    $prestr_parts[] = $k . "=" . $v;
}
$prestr = implode("&", $prestr_parts);

// 4. MD5加密（prestr + key）
$expected_sign = md5($prestr . $key);

writeNotifyLog("签名验证 - 原始字符串: $prestr");
writeNotifyLog("签名验证 - 期望签名: $expected_sign, 实际签名: $sign");

if ($sign !== $expected_sign) {
    writeNotifyLog("签名验证失败", 'ERROR');
    echo 'fail';
    exit;
}

// 只有TRADE_SUCCESS才是支付成功
if ($trade_status !== 'TRADE_SUCCESS') {
    writeNotifyLog("支付状态不是成功状态: $trade_status", 'WARNING');
    echo 'success'; // 仍然返回success表示接收到通知
    exit;
}

// 调用本地的set_order_payment_status.php来更新订单状态
try {
    // 构建请求URL
    $base_url = 'http://154.219.106.158:7893';
    $update_url = $base_url . '/set_order_payment_status.php?' . http_build_query([
        'order_id' => $out_trade_no,
        'payment_status' => 'paid'
    ]);
    
    writeNotifyLog("调用订单状态更新接口: $update_url");
    
    // 发起HTTP请求
    $opts = [
        'http' => [
            'timeout' => 30,
            'method' => 'GET',
            'header' => "User-Agent: PaymentNotifyBot/1.0\r\n"
        ]
    ];
    $context = stream_context_create($opts);
    $response = file_get_contents($update_url, false, $context);
    
    if ($response === false) {
        writeNotifyLog("调用订单状态更新接口失败", 'ERROR');
        echo 'fail';
        exit;
    }
    
    $result = json_decode($response, true);
    writeNotifyLog("订单状态更新结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));
    
    if ($result && $result['status'] === 'success') {
        writeNotifyLog("订单 $out_trade_no 支付状态更新成功", 'SUCCESS');
        echo 'success';
    } else {
        writeNotifyLog("订单状态更新失败: " . ($result['message'] ?? '未知错误'), 'ERROR');
        echo 'fail';
    }
    
} catch (Exception $e) {
    writeNotifyLog("处理支付通知异常: " . $e->getMessage(), 'ERROR');
    echo 'fail';
}
?>
