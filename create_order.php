<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$customer_contact = $_GET['customer_contact'] ?? '';
$product_id = $_GET['product_id'] ?? '';
$pay_type = $_GET['pay_type'] ?? 'wxpay';
if (!in_array($pay_type, ['alipay', 'wxpay'])) {
    $pay_type = 'wxpay';
}

if (empty($customer_contact)) {
    echo json_encode([
        'status' => 'error',
        'message' => '客户联系方式不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($product_id) || !is_numeric($product_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品ID不能为空且必须为数字',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 查询商品信息
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ? AND status != 'deleted'");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode([
            'status' => 'error',
            'message' => '商品不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查库存
    if ($product['stock_quantity'] <= 0) {
        echo json_encode([
            'status' => 'error',
            'message' => '商品库存不足',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 计算手续费，按6%加成，保留两位小数
    $base_price = floatval($product['product_price']);
    $final_price = round($base_price * 1.06, 2);
    
    // 生成订单号
    $order_id = 'ORDER' . time() . rand(1000, 9999);
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 创建订单记录 - 使用正确的字段名
    $stmt = $pdo->prepare("INSERT INTO orders (order_id, merchant_id, product_name, product_price, delivery_content, order_status, customer_contact) VALUES (?, ?, ?, ?, '', 'unpaid', ?)");
    $stmt->execute([
        $order_id,
        $product['merchant_id'],
        $product['product_name'],
        $final_price,
        $customer_contact
    ]);
    
    // 生成支付链接
    $payment_url = generatePaymentLink($order_id, $final_price, $product['product_name'], $pay_type);
    
    // 提交事务
    $pdo->commit();
    
    // 获取订单信息
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    // 未支付时隐藏发货内容
    $order['delivery_content'] = '*******';
    
    $result = [
        'order_info' => $order,
        'payment_url' => $payment_url,
        'pay_type' => $pay_type
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '订单创建成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'status' => 'error',
        'message' => '创建订单失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 生成支付链接函数
function generatePaymentLink($order_id, $amount, $product_name, $pay_type = 'wxpay') {
    // 支付网关配置
    $domain = "https://aftpay.njdzn.com/";
    $pid = "2222";
    $key = "4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA";
    
    $params = [
        "pid" => $pid,
        "type" => $pay_type,
        "out_trade_no" => $order_id,
        "notify_url" => "https://cloudshop.qnm6.top/notify.php",
        "return_url" => "https://cloudshop.qnm6.top/shop.php?dd=" . $order_id,
        "name" => "电商单号:".$order_id,
        "money" => $amount,
        "sitename" => "Mika云寄售",
        "sign_type" => "MD5"
    ];
    
    // 按照pay.py的签名算法
    // 1. 筛选参数（排除空值和以sign开头的参数）
    $para_filter = [];
    foreach ($params as $k => $v) {
        if ($v != "" && strpos($k, "sign") !== 0) {
            $para_filter[$k] = $v;
        }
    }
    
    // 2. 排序（按ASCII码递增排序）
    ksort($para_filter);
    
    // 3. 拼接参数（格式：key=value&key=value）
    $prestr_parts = [];
    foreach ($para_filter as $k => $v) {
        $prestr_parts[] = $k . "=" . $v;
    }
    $prestr = implode("&", $prestr_parts);
    
    // 4. MD5加密（prestr + key）
    $sign = md5($prestr . $key);
    $params["sign"] = $sign;
    
    return $domain . "/submit.php?" . http_build_query($params);
}
?> 